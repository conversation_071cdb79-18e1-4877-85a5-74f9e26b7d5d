// LLM模型管理模块
class ModelManager {
    constructor() {
        this.platforms = {
            openai: {
                name: 'OpenAI',
                baseUrl: 'https://api.openai.com',
                modelsEndpoint: '/v1/models',
                defaultModels: [
                    'gpt-4-vision-preview',
                    'gpt-4o',
                    'gpt-4o-mini'
                ]
            },
            anthropic: {
                name: 'Anthropic',
                baseUrl: 'https://api.anthropic.com',
                modelsEndpoint: null, // Anthropic不提供模型列表API
                defaultModels: [
                    'claude-3-5-sonnet-20241022',
                    'claude-3-opus-20240229',
                    'claude-3-sonnet-20240229',
                    'claude-3-haiku-20240307'
                ]
            },
            google: {
                name: 'Google',
                baseUrl: 'https://generativelanguage.googleapis.com',
                modelsEndpoint: '/v1beta/models',
                defaultModels: [
                    'gemini-1.5-flash',
                    'gemini-1.5-pro',
                    'gemini-2.0-flash-exp'
                ]
            }
        };
        
        this.modelCache = new Map();
        this.lastFetchTime = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    // 获取平台信息
    getPlatformInfo(platform) {
        return this.platforms[platform] || null;
    }

    // 获取所有支持的平台
    getSupportedPlatforms() {
        return Object.keys(this.platforms);
    }

    // 获取模型列表
    async getModels(platform, apiKey, baseUrl = null) {
        const platformInfo = this.getPlatformInfo(platform);
        if (!platformInfo) {
            throw new Error(`不支持的平台: ${platform}`);
        }

        // 检查缓存
        const cacheKey = `${platform}-${apiKey}`;
        const lastFetch = this.lastFetchTime.get(cacheKey);
        const cached = this.modelCache.get(cacheKey);
        
        if (cached && lastFetch && (Date.now() - lastFetch) < this.cacheTimeout) {
            return cached;
        }

        try {
            let models = [];
            
            if (platform === 'openai') {
                models = await this.fetchOpenAIModels(apiKey, baseUrl || platformInfo.baseUrl);
            } else if (platform === 'google') {
                models = await this.fetchGoogleModels(apiKey, baseUrl || platformInfo.baseUrl);
            } else if (platform === 'anthropic') {
                // Anthropic没有公开的模型列表API，使用默认列表
                models = platformInfo.defaultModels.map(model => ({
                    id: model,
                    name: model,
                    description: this.getModelDescription(model)
                }));
            }

            // 缓存结果
            this.modelCache.set(cacheKey, models);
            this.lastFetchTime.set(cacheKey, Date.now());
            
            return models;
        } catch (error) {
            console.warn(`获取${platform}模型列表失败:`, error);
            // 返回默认模型列表
            return platformInfo.defaultModels.map(model => ({
                id: model,
                name: model,
                description: this.getModelDescription(model),
                isDefault: true
            }));
        }
    }

    // 获取OpenAI模型列表
    async fetchOpenAIModels(apiKey, baseUrl) {
        const response = await fetch(`${baseUrl}/v1/models`, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`OpenAI API错误: ${response.status}`);
        }

        const data = await response.json();
        
        // 过滤出支持视觉的模型
        const visionModels = data.data.filter(model => 
            model.id.includes('vision') || 
            model.id.includes('gpt-4o') ||
            model.id === 'gpt-4-turbo'
        );

        return visionModels.map(model => ({
            id: model.id,
            name: model.id,
            description: this.getModelDescription(model.id),
            created: model.created,
            owned_by: model.owned_by
        }));
    }

    // 获取Google模型列表
    async fetchGoogleModels(apiKey, baseUrl) {
        const response = await fetch(`${baseUrl}/v1beta/models?key=${apiKey}`, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Google API错误: ${response.status}`);
        }

        const data = await response.json();
        
        // 过滤出支持视觉的Gemini模型
        const visionModels = data.models.filter(model => 
            model.name.includes('gemini') && 
            (model.supportedGenerationMethods?.includes('generateContent') ||
             model.name.includes('vision') ||
             model.name.includes('flash') ||
             model.name.includes('pro'))
        );

        return visionModels.map(model => ({
            id: model.name.replace('models/', ''),
            name: model.displayName || model.name.replace('models/', ''),
            description: model.description || this.getModelDescription(model.name),
            version: model.version,
            supportedMethods: model.supportedGenerationMethods
        }));
    }

    // 获取模型描述
    getModelDescription(modelId) {
        const descriptions = {
            'gpt-4-vision-preview': 'GPT-4 with vision capabilities (preview)',
            'gpt-4o': 'GPT-4 Omni - multimodal flagship model',
            'gpt-4o-mini': 'GPT-4 Omni Mini - faster and cheaper',
            'claude-3-5-sonnet-20241022': 'Claude 3.5 Sonnet - most intelligent model',
            'claude-3-opus-20240229': 'Claude 3 Opus - most powerful model',
            'claude-3-sonnet-20240229': 'Claude 3 Sonnet - balanced performance',
            'claude-3-haiku-20240307': 'Claude 3 Haiku - fastest model',
            'gemini-1.5-flash': 'Gemini 1.5 Flash - fast and efficient',
            'gemini-1.5-pro': 'Gemini 1.5 Pro - advanced reasoning',
            'gemini-2.0-flash-exp': 'Gemini 2.0 Flash Experimental - latest features'
        };

        return descriptions[modelId] || '多模态视觉语言模型';
    }

    // 验证自定义模型名称
    validateCustomModel(platform, modelName) {
        if (!modelName || typeof modelName !== 'string') {
            return { valid: false, error: '模型名称不能为空' };
        }

        const platformPatterns = {
            openai: /^(gpt-4|gpt-3\.5)/i,
            anthropic: /^claude-/i,
            google: /^gemini-/i
        };

        const pattern = platformPatterns[platform];
        if (pattern && !pattern.test(modelName)) {
            return { 
                valid: false, 
                error: `模型名称格式不正确，${platform}平台的模型应该以${this.getExpectedPrefix(platform)}开头` 
            };
        }

        return { valid: true };
    }

    // 获取期望的模型前缀
    getExpectedPrefix(platform) {
        const prefixes = {
            openai: 'gpt-',
            anthropic: 'claude-',
            google: 'gemini-'
        };
        return prefixes[platform] || '';
    }

    // 获取模型的详细信息
    getModelInfo(platform, modelId) {
        const platformInfo = this.getPlatformInfo(platform);
        if (!platformInfo) return null;

        return {
            platform: platformInfo.name,
            modelId: modelId,
            description: this.getModelDescription(modelId),
            apiEndpoint: this.getApiEndpoint(platform, modelId),
            maxTokens: this.getMaxTokens(platform, modelId),
            supportedFeatures: this.getSupportedFeatures(platform, modelId)
        };
    }

    // 获取API端点
    getApiEndpoint(platform, modelId) {
        const endpoints = {
            openai: '/v1/chat/completions',
            anthropic: '/v1/messages',
            google: `/v1beta/models/${modelId}:generateContent`
        };
        return endpoints[platform] || '';
    }

    // 获取最大Token数
    getMaxTokens(platform, modelId) {
        const maxTokens = {
            'gpt-4-vision-preview': 4096,
            'gpt-4o': 4096,
            'gpt-4o-mini': 16384,
            'claude-3-5-sonnet-20241022': 8192,
            'claude-3-opus-20240229': 4096,
            'claude-3-sonnet-20240229': 4096,
            'claude-3-haiku-20240307': 4096,
            'gemini-1.5-flash': 8192,
            'gemini-1.5-pro': 8192,
            'gemini-2.0-flash-exp': 8192
        };
        return maxTokens[modelId] || 4096;
    }

    // 获取支持的功能
    getSupportedFeatures(platform, modelId) {
        return {
            vision: true,
            text: true,
            streaming: platform !== 'google',
            functionCalling: platform === 'openai',
            systemPrompt: platform !== 'google'
        };
    }

    // 清除缓存
    clearCache() {
        this.modelCache.clear();
        this.lastFetchTime.clear();
    }
}

// 导出模型管理器
window.ModelManager = ModelManager;
