<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR插件自动识别功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .result {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>OCR插件自动识别功能测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>本页面用于测试OCR插件的图片文件自动识别功能优化。</p>
        <p><strong>注意：</strong>此测试页面需要在uTools环境中运行才能正常工作。</p>
    </div>

    <div class="test-section">
        <h2>Payload格式测试</h2>
        <p>测试不同格式的payload数据是否能正确提取文件路径：</p>
        
        <button class="test-button" onclick="testArrayPayload()">测试数组格式</button>
        <button class="test-button" onclick="testObjectPayload()">测试对象格式</button>
        <button class="test-button" onclick="testStringPayload()">测试字符串格式</button>
        <button class="test-button" onclick="testComplexPayload()">测试复杂对象格式</button>
        
        <div id="payload-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>文件路径标准化测试</h2>
        <p>测试文件路径标准化功能：</p>
        
        <button class="test-button" onclick="testPathNormalization()">测试路径标准化</button>
        
        <div id="path-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>模拟uTools环境测试</h2>
        <p>模拟uTools环境下的插件触发：</p>
        
        <button class="test-button" onclick="simulateFilesTrigger()">模拟文件触发</button>
        <button class="test-button" onclick="simulateClipboardTrigger()">模拟剪切板触发</button>
        
        <div id="simulation-result" class="result"></div>
    </div>

    <script>
        // 模拟OCR插件的核心方法（用于测试）
        class MockOCRPlugin {
            extractImageFilePath(payload) {
                try {
                    // 处理文件路径数组（来自文件拖拽或选择）
                    if (Array.isArray(payload)) {
                        const filePath = payload[0];
                        if (typeof filePath === 'string' && filePath.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                            return filePath;
                        }
                    }
                    
                    // 处理对象格式的payload
                    if (typeof payload === 'object' && payload !== null) {
                        // 处理files类型
                        if (payload.type === 'files' && payload.data) {
                            const filePath = Array.isArray(payload.data) ? payload.data[0] : payload.data;
                            if (typeof filePath === 'string' && filePath.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                                return filePath;
                            }
                        }
                        
                        // 检查是否有文件路径相关的属性
                        const possiblePaths = [payload.path, payload.file, payload.src, payload.url, payload.filePath];
                        for (const path of possiblePaths) {
                            if (typeof path === 'string' && path.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                                return path;
                            }
                        }
                    }
                    
                    // 如果是字符串，检查是否是文件路径
                    if (typeof payload === 'string' && payload.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                        return payload;
                    }
                    
                    return null;
                } catch (error) {
                    console.error('提取文件路径失败:', error);
                    return null;
                }
            }

            normalizeFilePath(filePath) {
                try {
                    // 处理Windows路径
                    let normalizedPath = filePath.replace(/\\/g, '/');
                    
                    // 如果不是以file://开头，添加file://协议
                    if (!normalizedPath.startsWith('file://')) {
                        // 处理绝对路径
                        if (normalizedPath.match(/^[a-zA-Z]:/)) {
                            // Windows绝对路径 (C:/path/to/file)
                            normalizedPath = 'file:///' + normalizedPath;
                        } else if (normalizedPath.startsWith('/')) {
                            // Unix绝对路径 (/path/to/file)
                            normalizedPath = 'file://' + normalizedPath;
                        } else {
                            // 相对路径，添加当前目录
                            normalizedPath = 'file:///' + normalizedPath;
                        }
                    }
                    
                    return normalizedPath;
                } catch (error) {
                    console.error('路径标准化失败:', error);
                    return filePath;
                }
            }
        }

        const mockPlugin = new MockOCRPlugin();

        function testArrayPayload() {
            const testCases = [
                ["C:/Users/<USER>/image.jpg"],
                ["D:\\Pictures\\test.png"],
                ["/home/<USER>/photo.jpeg"],
                ["./local/image.gif"]
            ];

            let results = "数组格式测试结果:\n";
            testCases.forEach((payload, index) => {
                const result = mockPlugin.extractImageFilePath(payload);
                results += `测试 ${index + 1}: ${JSON.stringify(payload)} -> ${result}\n`;
            });

            document.getElementById('payload-result').textContent = results;
            document.getElementById('payload-result').className = 'result success';
        }

        function testObjectPayload() {
            const testCases = [
                { type: 'files', data: ["C:/test/image.jpg"] },
                { type: 'files', data: "D:/photo.png" },
                { path: "C:/Users/<USER>" },
                { file: "/home/<USER>", src: "backup.jpg" }
            ];

            let results = "对象格式测试结果:\n";
            testCases.forEach((payload, index) => {
                const result = mockPlugin.extractImageFilePath(payload);
                results += `测试 ${index + 1}: ${JSON.stringify(payload)} -> ${result}\n`;
            });

            document.getElementById('payload-result').textContent = results;
            document.getElementById('payload-result').className = 'result success';
        }

        function testStringPayload() {
            const testCases = [
                "C:/Users/<USER>/image.jpg",
                "D:\\Pictures\\test.png",
                "/home/<USER>/photo.jpeg",
                "not-an-image.txt",
                "image-without-extension"
            ];

            let results = "字符串格式测试结果:\n";
            testCases.forEach((payload, index) => {
                const result = mockPlugin.extractImageFilePath(payload);
                results += `测试 ${index + 1}: "${payload}" -> ${result}\n`;
            });

            document.getElementById('payload-result').textContent = results;
            document.getElementById('payload-result').className = 'result success';
        }

        function testComplexPayload() {
            const testCases = [
                { type: 'img', data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==' },
                { type: 'files', data: ["C:/test.jpg", "D:/backup.png"] },
                { filePath: "E:/images/photo.webp", metadata: { size: 1024 } }
            ];

            let results = "复杂对象格式测试结果:\n";
            testCases.forEach((payload, index) => {
                const result = mockPlugin.extractImageFilePath(payload);
                results += `测试 ${index + 1}: ${JSON.stringify(payload).substring(0, 100)}... -> ${result}\n`;
            });

            document.getElementById('payload-result').textContent = results;
            document.getElementById('payload-result').className = 'result success';
        }

        function testPathNormalization() {
            const testPaths = [
                "C:\\Users\\<USER>\\image.jpg",
                "D:/Pictures/photo.png",
                "/home/<USER>/image.jpeg",
                "./local/image.gif",
                "file:///C:/already/normalized.jpg"
            ];

            let results = "路径标准化测试结果:\n";
            testPaths.forEach((path, index) => {
                const result = mockPlugin.normalizeFilePath(path);
                results += `测试 ${index + 1}: "${path}" -> "${result}"\n`;
            });

            document.getElementById('path-result').textContent = results;
            document.getElementById('path-result').className = 'result info';
        }

        function simulateFilesTrigger() {
            // 模拟通过文件触发插件的情况
            const mockPayload = {
                type: 'files',
                data: ["C:/Users/<USER>/screenshot.png"]
            };

            let results = "模拟文件触发测试:\n";
            results += `Payload: ${JSON.stringify(mockPayload)}\n`;
            
            const extractedPath = mockPlugin.extractImageFilePath(mockPayload);
            results += `提取的文件路径: ${extractedPath}\n`;
            
            if (extractedPath) {
                const normalizedPath = mockPlugin.normalizeFilePath(extractedPath);
                results += `标准化路径: ${normalizedPath}\n`;
                results += "✓ 应该会自动开始OCR识别\n";
            } else {
                results += "✗ 无法提取文件路径，会显示手动选择界面\n";
            }

            document.getElementById('simulation-result').textContent = results;
            document.getElementById('simulation-result').className = 'result info';
        }

        function simulateClipboardTrigger() {
            // 模拟剪切板图片触发的情况
            const mockPayload = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

            let results = "模拟剪切板触发测试:\n";
            results += `Payload类型: ${typeof mockPayload}\n`;
            results += `是否为base64图片: ${mockPayload.startsWith('data:image/')}\n`;
            
            const extractedPath = mockPlugin.extractImageFilePath(mockPayload);
            results += `提取的文件路径: ${extractedPath}\n`;
            results += "✓ 应该直接进行OCR识别（base64数据）\n";

            document.getElementById('simulation-result').textContent = results;
            document.getElementById('simulation-result').className = 'result success';
        }

        // 页面加载完成后显示初始信息
        window.onload = function() {
            document.getElementById('payload-result').textContent = '点击上方按钮开始测试...';
            document.getElementById('path-result').textContent = '点击上方按钮开始测试...';
            document.getElementById('simulation-result').textContent = '点击上方按钮开始测试...';
        };
    </script>
</body>
</html>
