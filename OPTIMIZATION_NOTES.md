# uTools 插件优化说明

本次优化按照 uTools 官方文档的最佳实践对代码进行了改进，主要包括以下几个方面：

## 1. 异步操作优化

### 替换 setTimeout 为 requestAnimationFrame
- **原因**: `requestAnimationFrame` 更适合 DOM 操作相关的异步任务，能确保在浏览器下一次重绘前执行
- **优化位置**:
  - `init()` 方法中的 payload 检查
  - `handlePluginEnter()` 方法中的截图触发
  - `checkForPayload()` 方法中的各种功能触发

### 保留必要的 setTimeout
- 在 `handleClipboardImage()` 中保留 2 秒延迟，给用户足够时间阅读错误信息

## 2. API 调用安全性增强

### preload.js 优化
- 添加 `utools` API 可用性检查
- 改进错误处理，使用 `console.error` 替代 `console.log`
- 添加开发环境兼容性警告
- 为所有 uTools API 调用添加安全检查

### 具体改进
```javascript
// 优化前
utools.screenCapture(callback);

// 优化后
if (typeof utools !== 'undefined' && utools.screenCapture) {
    utools.screenCapture(callback);
} else {
    console.error('screenCapture API 不可用');
    if (callback) callback(null);
}
```

## 3. 插件配置优化

### plugin.json 改进
- 添加 `resizable: false` 配置，提供更好的用户体验
- 为 `over` 类型命令添加 `fileType: "img"` 属性，明确支持的文件类型
- 统一图标路径配置

## 4. 错误处理改进

### 统一错误处理
- 使用 `console.error` 替代 `console.log` 记录错误
- 添加更详细的错误信息
- 改进 try-catch 块的错误处理

### DOM 操作安全性
- 在操作 DOM 元素前添加存在性检查
- 使用 `requestAnimationFrame` 确保 DOM 已准备就绪

## 5. 代码结构优化

### checkForPayload 方法重构
- 根据不同的功能代码采用不同的处理策略
- 简化逻辑流程，提高代码可读性
- 统一使用 `requestAnimationFrame` 进行异步操作

### 事件处理优化
- 改进插件进入和退出事件的处理
- 添加更完善的状态管理

## 6. 符合官方规范的特性

### 遵循 uTools 最佳实践
1. **预加载脚本精简**: preload.js 代码简洁明了，易于审核
2. **API 使用规范**: 正确使用 `onPluginEnter`、`onPluginOut` 等生命周期方法
3. **错误处理完善**: 添加了完整的错误处理和降级方案
4. **性能优化**: 使用 `requestAnimationFrame` 优化 DOM 操作时机
5. **配置规范**: plugin.json 配置符合官方文档要求

### 开发环境兼容性
- 添加了开发环境下的兼容性处理
- 在 uTools API 不可用时提供友好的错误提示

## 7. 用户体验改进

### 界面交互优化
- 确保文件选择在 DOM 准备就绪后触发
- 优化截图功能的触发时机
- 改进剪切板处理的响应速度

### 错误反馈优化
- 提供更清晰的错误信息
- 在出错时提供备选操作方案

## 总结

经过本次优化，插件代码更加符合 uTools 官方规范，具有更好的稳定性、性能和用户体验。主要改进包括：

- ✅ 异步操作使用 `requestAnimationFrame` 替代不必要的 `setTimeout`
- ✅ API 调用添加安全检查和错误处理
- ✅ 插件配置符合官方规范
- ✅ 代码结构更加清晰和可维护
- ✅ 开发环境兼容性更好
- ✅ 用户体验得到改善

这些优化确保了插件能够稳定运行在 uTools 环境中，并且符合官方插件市场的审核要求。