// OCR服务封装模块
class OCRServices {
    constructor() {
        this.supportedServices = ['baidu', 'tencent', 'aliyun', 'llm'];
    }

    // 执行OCR识别
    async performOCR(imageBase64, service, config) {
        if (!this.supportedServices.includes(service)) {
            return { success: false, error: '不支持的OCR服务' };
        }

        try {
            switch (service) {
                case 'baidu':
                    return await this.baiduOCR(imageBase64, config);
                case 'tencent':
                    return await this.tencentOCR(imageBase64, config);
                case 'aliyun':
                    return await this.aliyunOCR(imageBase64, config);
                case 'llm':
                    return await this.llmOCR(imageBase64, config);
                default:
                    return { success: false, error: '不支持的OCR服务' };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 百度OCR
    async baiduOCR(imageBase64, config) {
        try {
            // 获取access_token
            const accessToken = await this.getBaiduAccessToken(config.apiKey, config.secretKey);
            
            // 执行OCR
            const ocrUrl = `https://aip.baidubce.com/rest/2.0/ocr/v1/${config.type}`;
            const formData = new FormData();
            formData.append('image', imageBase64.replace(/^data:image\/[a-z]+;base64,/, ''));
            formData.append('access_token', accessToken);

            const response = await fetch(ocrUrl, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.words_result) {
                const text = result.words_result.map(item => item.words).join('\n');
                return { 
                    success: true, 
                    text,
                    confidence: this.calculateAverageConfidence(result.words_result),
                    details: result.words_result
                };
            } else {
                return { success: false, error: result.error_msg || '识别失败' };
            }
        } catch (error) {
            return { success: false, error: '百度OCR识别失败: ' + error.message };
        }
    }

    // 获取百度access_token
    async getBaiduAccessToken(apiKey, secretKey) {
        const tokenUrl = 'https://aip.baidubce.com/oauth/2.0/token';
        const params = new URLSearchParams({
            grant_type: 'client_credentials',
            client_id: apiKey,
            client_secret: secretKey
        });

        const response = await fetch(`${tokenUrl}?${params}`, { method: 'POST' });
        const result = await response.json();
        
        if (result.access_token) {
            return result.access_token;
        } else {
            throw new Error(result.error_description || '获取access_token失败');
        }
    }

    // 腾讯云OCR
    async tencentOCR(imageBase64, config) {
        try {
            const endpoint = 'ocr.tencentcloudapi.com';
            const service = 'ocr';
            const version = '2018-11-19';
            const action = 'GeneralBasicOCR';
            const region = config.region || 'ap-beijing';

            const payload = {
                ImageBase64: imageBase64.replace(/^data:image\/[a-z]+;base64,/, '')
            };

            const headers = await this.getTencentHeaders(
                config.secretId,
                config.secretKey,
                endpoint,
                service,
                version,
                action,
                region,
                payload
            );

            const response = await fetch(`https://${endpoint}`, {
                method: 'POST',
                headers,
                body: JSON.stringify(payload)
            });

            const result = await response.json();
            
            if (result.Response && result.Response.TextDetections) {
                const text = result.Response.TextDetections.map(item => item.DetectedText).join('\n');
                return { 
                    success: true, 
                    text,
                    confidence: this.calculateAverageConfidence(result.Response.TextDetections, 'Confidence'),
                    details: result.Response.TextDetections
                };
            } else {
                return { success: false, error: result.Response?.Error?.Message || '识别失败' };
            }
        } catch (error) {
            return { success: false, error: '腾讯云OCR识别失败: ' + error.message };
        }
    }

    // 生成腾讯云请求头
    async getTencentHeaders(secretId, secretKey, host, service, version, action, region, payload) {
        const timestamp = Math.floor(Date.now() / 1000);
        const date = new Date(timestamp * 1000).toISOString().substring(0, 10);
        
        // 构建规范请求串
        const canonicalRequest = [
            'POST',
            '/',
            '',
            `content-type:application/json; charset=utf-8\nhost:${host}\n`,
            'content-type;host',
            await this.sha256(JSON.stringify(payload))
        ].join('\n');

        // 构建待签名字符串
        const algorithm = 'TC3-HMAC-SHA256';
        const stringToSign = [
            algorithm,
            timestamp,
            `${date}/${service}/tc3_request`,
            await this.sha256(canonicalRequest)
        ].join('\n');

        // 计算签名
        const secretDate = await this.hmacSha256(`TC3${secretKey}`, date);
        const secretService = await this.hmacSha256(secretDate, service);
        const secretSigning = await this.hmacSha256(secretService, 'tc3_request');
        const signature = await this.hmacSha256(secretSigning, stringToSign, 'hex');

        const authorization = `${algorithm} Credential=${secretId}/${date}/${service}/tc3_request, SignedHeaders=content-type;host, Signature=${signature}`;

        return {
            'Authorization': authorization,
            'Content-Type': 'application/json; charset=utf-8',
            'Host': host,
            'X-TC-Action': action,
            'X-TC-Timestamp': timestamp.toString(),
            'X-TC-Version': version,
            'X-TC-Region': region
        };
    }

    // LLM视觉模型OCR
    async llmOCR(imageBase64, config) {
        try {
            let apiUrl, headers, payload;
            const platform = config.platform || 'openai';
            const model = config.model || 'gpt-4-vision-preview';

            if (platform === 'openai') {
                apiUrl = (config.baseUrl || 'https://api.openai.com') + '/v1/chat/completions';
                headers = {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json'
                };
                payload = {
                    model: model,
                    messages: [{
                        role: 'user',
                        content: [
                            { type: 'text', text: config.prompt || '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。' },
                            { type: 'image_url', image_url: { url: imageBase64 } }
                        ]
                    }],
                    max_tokens: config.maxTokens || 1000
                };
            } else if (platform === 'anthropic') {
                apiUrl = (config.baseUrl || 'https://api.anthropic.com') + '/v1/messages';
                headers = {
                    'x-api-key': config.apiKey,
                    'Content-Type': 'application/json',
                    'anthropic-version': '2023-06-01'
                };
                payload = {
                    model: model,
                    max_tokens: config.maxTokens || 1000,
                    messages: [{
                        role: 'user',
                        content: [
                            { type: 'text', text: config.prompt || '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。' },
                            {
                                type: 'image',
                                source: {
                                    type: 'base64',
                                    media_type: 'image/png',
                                    data: imageBase64.replace(/^data:image\/[a-z]+;base64,/, '')
                                }
                            }
                        ]
                    }]
                };
            } else if (platform === 'google') {
                // 使用指定的Gemini模型
                apiUrl = (config.baseUrl || 'https://generativelanguage.googleapis.com') + `/v1beta/models/${model}:generateContent`;
                headers = {
                    'Content-Type': 'application/json'
                };
                payload = {
                    contents: [{
                        parts: [
                            { text: config.prompt || '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。' },
                            {
                                inline_data: {
                                    mime_type: 'image/png',
                                    data: imageBase64.replace(/^data:image\/[a-z]+;base64,/, '')
                                }
                            }
                        ]
                    }]
                };
                // Gemini使用API key作为查询参数
                apiUrl += `?key=${config.apiKey}`;
            }

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers,
                body: JSON.stringify(payload)
            });

            const result = await response.json();
            let text = '';

            if (platform === 'openai' && result.choices?.[0]?.message?.content) {
                text = result.choices[0].message.content;
            } else if (platform === 'anthropic' && result.content?.[0]?.text) {
                text = result.content[0].text;
            } else if (platform === 'google' && result.candidates?.[0]?.content?.parts?.[0]?.text) {
                text = result.candidates[0].content.parts[0].text;
            }

            if (text) {
                return { success: true, text: text.trim() };
            } else {
                return { success: false, error: result.error?.message || '识别失败' };
            }
        } catch (error) {
            return { success: false, error: 'LLM OCR识别失败: ' + error.message };
        }
    }

    // 阿里云OCR (简化实现)
    async aliyunOCR(imageBase64, config) {
        // 阿里云OCR实现较复杂，这里提供基本框架
        // 实际使用时需要完整的阿里云SDK或签名算法
        console.log('阿里云OCR调用', { imageLength: imageBase64.length, config: config.accessKey ? '已配置' : '未配置' });
        return { success: false, error: '阿里云OCR暂未实现，请使用其他服务' };
    }

    // 计算平均置信度
    calculateAverageConfidence(results, confidenceField = 'probability') {
        if (!results || results.length === 0) return null;
        
        const confidences = results
            .map(item => item[confidenceField])
            .filter(conf => conf !== undefined && conf !== null);
        
        if (confidences.length === 0) return null;
        
        const average = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
        return Math.round(average * 100) / 100; // 保留两位小数
    }

    // SHA256哈希
    async sha256(message) {
        const msgBuffer = new TextEncoder().encode(message);
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // HMAC-SHA256
    async hmacSha256(key, message, outputFormat = 'buffer') {
        const keyBuffer = typeof key === 'string' ? new TextEncoder().encode(key) : key;
        const messageBuffer = new TextEncoder().encode(message);
        
        const cryptoKey = await crypto.subtle.importKey(
            'raw',
            keyBuffer,
            { name: 'HMAC', hash: 'SHA-256' },
            false,
            ['sign']
        );
        
        const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageBuffer);
        
        if (outputFormat === 'hex') {
            const hashArray = Array.from(new Uint8Array(signature));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }
        
        return signature;
    }
}

// 导出OCR服务
window.OCRServices = OCRServices;
